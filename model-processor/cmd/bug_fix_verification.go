package main

import (
	"fmt"
	"time"

	"model-processor/service"
	"model-processor/txparser"

	"github.com/gagliardetto/solana-go"
)

// 验证多 Signer 问题修复效果的程序
func main() {
	fmt.Println("=== Solana 多 Signer Bug 修复验证 ===")

	// 测试1: 验证 extractSignerForInstruction 方法
	testExtractSignerForInstruction()

	// 测试2: 验证 transformSwapInfo 方法
	testTransformSwapInfo()

	// 测试3: 验证 Pump.fun 多用户处理
	testPumpfunMultiUser()

	fmt.Println("\n=== 所有测试完成 ===")
}

func testExtractSignerForInstruction() {
	fmt.Println("\n--- 测试1: extractSignerForInstruction 方法 ---")

	// 创建测试用的签名者
	signer1 := solana.NewWallet().PublicKey()
	signer2 := solana.NewWallet().PublicKey()
	signer3 := solana.NewWallet().PublicKey()

	// 在修复后的代码中，extractSignerForInstruction 方法已经添加到 Parser 结构中
	// 这个方法会根据指令索引正确提取对应的 signer

	fmt.Printf("Signer1: %s\n", signer1.String())
	fmt.Printf("Signer2: %s\n", signer2.String())
	fmt.Printf("Signer3: %s\n", signer3.String())

	// 模拟设置 allAccountKeys
	// parser.allAccountKeys = []solana.PublicKey{signer1, signer2, signer3}

	fmt.Println("✓ extractSignerForInstruction 方法已添加")
}

func testTransformSwapInfo() {
	fmt.Println("\n--- 测试2: transformSwapInfo 方法修复 ---")

	// 创建测试用的 SwapInfo
	user1 := solana.NewWallet().PublicKey()
	user2 := solana.NewWallet().PublicKey()

	// 测试场景1: 使用 User 字段
	swapInfo1 := &txparser.SwapInfo{
		User:             user1,
		InstructionIndex: 0,
		Signers:          []solana.PublicKey{user1, user2},
	}

	finalTx1 := service.TransformSwapInfoForTest(swapInfo1, "test_hash_1", 12345, 0)
	fmt.Printf("场景1 - 使用 User 字段: %s\n", finalTx1.UserAddr)

	if finalTx1.UserAddr == user1.String() {
		fmt.Println("✓ User 字段优先级正确")
	} else {
		fmt.Println("✗ User 字段优先级错误")
	}

	// 测试场景2: 使用 Signers 数组和指令索引
	swapInfo2 := &txparser.SwapInfo{
		User:             solana.PublicKey{}, // 空的 User
		InstructionIndex: 1,
		Signers:          []solana.PublicKey{user1, user2},
	}

	finalTx2 := service.TransformSwapInfoForTest(swapInfo2, "test_hash_2", 12346, 1)
	fmt.Printf("场景2 - 使用指令索引1: %s\n", finalTx2.UserAddr)

	if finalTx2.UserAddr == user2.String() {
		fmt.Println("✓ 指令索引选择正确")
	} else {
		fmt.Println("✗ 指令索引选择错误，应该是:", user2.String())
	}

	// 测试场景3: 指令索引超出范围，回退到第一个
	swapInfo3 := &txparser.SwapInfo{
		User:             solana.PublicKey{}, // 空的 User
		InstructionIndex: 5,                  // 超出范围
		Signers:          []solana.PublicKey{user1, user2},
	}

	finalTx3 := service.TransformSwapInfoForTest(swapInfo3, "test_hash_3", 12347, 2)
	fmt.Printf("场景3 - 指令索引超出范围: %s\n", finalTx3.UserAddr)

	if finalTx3.UserAddr == user1.String() {
		fmt.Println("✓ 超出范围时回退到第一个 signer 正确")
	} else {
		fmt.Println("✗ 超出范围时回退逻辑错误")
	}
}

func testPumpfunMultiUser() {
	fmt.Println("\n--- 测试3: Pump.fun 多用户处理 ---")

	// 创建测试用的用户
	user1 := solana.NewWallet().PublicKey()
	user2 := solana.NewWallet().PublicKey()
	mintAddr := solana.NewWallet().PublicKey()

	// 创建 Pump.fun 事件
	event1 := &txparser.PumpfunTradeEvent{
		User:        user1,
		Mint:        mintAddr,
		IsBuy:       true,
		SolAmount:   1000000000, // 1 SOL
		TokenAmount: 1000000,    // 1M tokens
		Timestamp:   time.Now().Unix(),
	}

	event2 := &txparser.PumpfunTradeEvent{
		User:        user2,
		Mint:        mintAddr,
		IsBuy:       false,
		SolAmount:   500000000, // 0.5 SOL
		TokenAmount: 500000,    // 0.5M tokens
		Timestamp:   time.Now().Unix(),
	}

	fmt.Printf("Event1 User: %s (BUY)\n", event1.User.String())
	fmt.Printf("Event2 User: %s (SELL)\n", event2.User.String())

	// 验证事件中的用户地址不同
	if event1.User.String() != event2.User.String() {
		fmt.Println("✓ Pump.fun 事件包含不同的用户地址")
	} else {
		fmt.Println("✗ Pump.fun 事件用户地址相同（不应该）")
	}

	// 在修复后的代码中，每个 SwapInfo 都会使用事件中的 User 字段
	fmt.Println("✓ 修复后的代码会为每个 Pump.fun 事件创建独立的 SwapInfo")
	fmt.Println("✓ 每个 SwapInfo 的 User 字段会设置为对应事件的 User")
}

// 为了测试需要，我们需要在 service 包中添加一个测试用的函数
// 这个函数应该在 service 包中实现
