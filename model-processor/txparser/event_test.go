package txparser

import (
	"bytes"
	"testing"

	ag_binary "github.com/gagliardetto/binary"
	"github.com/gagliardetto/solana-go"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

// TestPumpfunTradeEventParsing 测试PumpFun TradeEvent的解析
func TestPumpfunTradeEventParsing(t *testing.T) {
	// 创建测试数据 - 模拟包含CreatorFee字段的事件
	testEventWithCreatorFee := PumpfunTradeEvent{
		Mint:                  solana.MustPublicKeyFromBase58("DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"),
		SolAmount:             1000000000, // 1 SOL
		TokenAmount:           1000000,    // 1M tokens
		IsBuy:                 true,
		User:                  solana.MustPublicKeyFromBase58("11111111111111111111111111111112"),
		Timestamp:             1703123456,
		VirtualSolReserves:    30000000000,
		VirtualTokenReserves:  1073000000000000,
		RealSolReserves:       85123456789,
		RealTokenReserves:     206123456789,
		FeeRecipient:          solana.MustPublicKeyFromBase58("CebN5WGQ4jvEPvsVU4EoHEpgzq1VV2AbicfhtW4xC9iM"),
		FeeBasisPoints:        100,
		Fee:                   10000000,
		Creator:               solana.MustPublicKeyFromBase58("39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg"),
		CreatorFeeBasisPoints: 100,
		CreatorFee:            10000000, // 这个字段在新IDL中可能不存在
	}

	// 序列化测试数据
	encoder := ag_binary.NewBorshEncoder()
	err := encoder.Encode(testEventWithCreatorFee)
	assert.NoError(t, err)

	serializedData := encoder.ToBytes()

	// 创建模拟的Parser
	parser := &Parser{
		Log: &MockLogger{},
	}

	// 测试解析包含CreatorFee字段的数据
	t.Run("ParseWithCreatorFee", func(t *testing.T) {
		result, err := handlePumpfunTradeEvent(serializedData, parser)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, testEventWithCreatorFee.Mint, result.Mint)
		assert.Equal(t, testEventWithCreatorFee.SolAmount, result.SolAmount)
		assert.Equal(t, testEventWithCreatorFee.IsBuy, result.IsBuy)
		assert.Equal(t, testEventWithCreatorFee.CreatorFee, result.CreatorFee)
	})

	// 创建不包含CreatorFee字段的测试数据（模拟新IDL格式）
	testEventWithoutCreatorFee := struct {
		Mint                  solana.PublicKey `json:"mint"`
		SolAmount             uint64           `json:"solAmount"`
		TokenAmount           uint64           `json:"tokenAmount"`
		IsBuy                 bool             `json:"isBuy"`
		User                  solana.PublicKey `json:"user"`
		Timestamp             int64            `json:"timestamp"`
		VirtualSolReserves    uint64           `json:"virtualSolReserves"`
		VirtualTokenReserves  uint64           `json:"virtualTokenReserves"`
		RealSolReserves       uint64           `json:"realSolReserves"`
		RealTokenReserves     uint64           `json:"realTokenReserves"`
		FeeRecipient          solana.PublicKey `json:"feeRecipient"`
		FeeBasisPoints        uint64           `json:"feeBasisPoints"`
		Fee                   uint64           `json:"fee"`
		Creator               solana.PublicKey `json:"creator"`
		CreatorFeeBasisPoints uint64           `json:"creatorFeeBasisPoints"`
		// 注意：不包含CreatorFee字段
	}{
		Mint:                  testEventWithCreatorFee.Mint,
		SolAmount:             testEventWithCreatorFee.SolAmount,
		TokenAmount:           testEventWithCreatorFee.TokenAmount,
		IsBuy:                 testEventWithCreatorFee.IsBuy,
		User:                  testEventWithCreatorFee.User,
		Timestamp:             testEventWithCreatorFee.Timestamp,
		VirtualSolReserves:    testEventWithCreatorFee.VirtualSolReserves,
		VirtualTokenReserves:  testEventWithCreatorFee.VirtualTokenReserves,
		RealSolReserves:       testEventWithCreatorFee.RealSolReserves,
		RealTokenReserves:     testEventWithCreatorFee.RealTokenReserves,
		FeeRecipient:          testEventWithCreatorFee.FeeRecipient,
		FeeBasisPoints:        testEventWithCreatorFee.FeeBasisPoints,
		Fee:                   testEventWithCreatorFee.Fee,
		Creator:               testEventWithCreatorFee.Creator,
		CreatorFeeBasisPoints: testEventWithCreatorFee.CreatorFeeBasisPoints,
	}

	// 序列化不包含CreatorFee的数据
	encoder2 := ag_binary.NewBorshEncoder()
	err = encoder2.Encode(testEventWithoutCreatorFee)
	assert.NoError(t, err)

	serializedDataV2 := encoder2.ToBytes()

	// 测试解析不包含CreatorFee字段的数据
	t.Run("ParseWithoutCreatorFee", func(t *testing.T) {
		result, err := handlePumpfunTradeEvent(serializedDataV2, parser)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, testEventWithCreatorFee.Mint, result.Mint)
		assert.Equal(t, testEventWithCreatorFee.SolAmount, result.SolAmount)
		assert.Equal(t, testEventWithCreatorFee.IsBuy, result.IsBuy)
		assert.Equal(t, uint64(0), result.CreatorFee) // 应该是默认值0
	})
}

// TestPumpfunCreateEventParsing 测试PumpFun CreateEvent的解析
func TestPumpfunCreateEventParsing(t *testing.T) {
	testCreateEvent := PumpfunCreateEvent{
		Name:         "Test Token",
		Symbol:       "TEST",
		Uri:          "https://example.com/metadata.json",
		Mint:         solana.MustPublicKeyFromBase58("DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"),
		BondingCurve: solana.MustPublicKeyFromBase58("11111111111111111111111111111112"),
		User:         solana.MustPublicKeyFromBase58("39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg"),
	}

	// 序列化测试数据
	encoder := ag_binary.NewBorshEncoder()
	err := encoder.Encode(testCreateEvent)
	assert.NoError(t, err)

	serializedData := encoder.ToBytes()
	decoder := ag_binary.NewBorshDecoder(serializedData)

	// 测试解析
	result, err := handlePumpfunCreateEvent(decoder)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, testCreateEvent.Name, result.Name)
	assert.Equal(t, testCreateEvent.Symbol, result.Symbol)
	assert.Equal(t, testCreateEvent.Uri, result.Uri)
	assert.Equal(t, testCreateEvent.Mint, result.Mint)
	assert.Equal(t, testCreateEvent.BondingCurve, result.BondingCurve)
	assert.Equal(t, testCreateEvent.User, result.User)
}

// MockLogger 模拟日志记录器
type MockLogger struct{}

func (m *MockLogger) Debugf(format string, args ...interface{}) {}
func (m *MockLogger) Infof(format string, args ...interface{})  {}
func (m *MockLogger) Warnf(format string, args ...interface{})  {}
func (m *MockLogger) Errorf(format string, args ...interface{}) {}
