package txparser

import (
	"fmt"
	"model-processor/proto"

	ag_binary "github.com/gagliardetto/binary"
	"github.com/gagliardetto/solana-go"
)

type InputTransfer struct {
	TransferData
}

type OutputTransfer struct {
	TransferData
}

type PumpSwapCreateEvent struct {
	Timestamp             int64            `json:"timestamp"`
	Index                 uint16           `json:"index"`
	Creator               solana.PublicKey `json:"creator"`
	BaseMint              solana.PublicKey `json:"baseMint"`
	QuoteMint             solana.PublicKey `json:"quoteMint"`
	BaseMintDecimals      uint8            `json:"baseMintDecimals"`
	QuoteMintDecimals     uint8            `json:"quoteMintDecimals"`
	BaseAmountIn          uint64           `json:"baseAmountIn"`
	QuoteAmountIn         uint64           `json:"quoteAmountIn"`
	PoolBaseAmount        uint64           `json:"poolBaseAmount"`
	PoolQuoteAmount       uint64           `json:"poolQuoteAmount"`
	MinimumLiquidity      uint64           `json:"minimumLiquidity"`
	InitialLiquidity      uint64           `json:"initialLiquidity"`
	LpTokenAmountOut      uint64           `json:"lpTokenAmountOut"`
	PoolBump              uint8            `json:"poolBump"`
	Pool                  solana.PublicKey `json:"pool"`
	LpMint                solana.PublicKey `json:"lpMint"`
	UserBaseTokenAccount  solana.PublicKey `json:"userBaseTokenAccount"`
	UserQuoteTokenAccount solana.PublicKey `json:"userQuoteTokenAccount"`
}
type PumpSwapBuyEvent struct {
	Timestamp                        int64            `json:"timestamp"`
	BaseAmountOut                    uint64           `json:"baseAmountOut"`
	MaxQuoteAmountIn                 uint64           `json:"maxQuoteAmountIn"`
	UserBaseTokenReserves            uint64           `json:"userBaseTokenReserves"`
	UserQuoteTokenReserves           uint64           `json:"userQuoteTokenReserves"`
	PoolBaseTokenReserves            uint64           `json:"poolBaseTokenReserves"`
	PoolQuoteTokenReserves           uint64           `json:"poolQuoteTokenReserves"`
	QuoteAmountIn                    uint64           `json:"quoteAmountIn"`
	LpFeeBasisPoints                 uint64           `json:"lpFeeBasisPoints"`
	LpFee                            uint64           `json:"lpFee"`
	ProtocolFeeBasisPoints           uint64           `json:"protocolFeeBasisPoints"`
	ProtocolFee                      uint64           `json:"protocolFee"`
	QuoteAmountInWithLpFee           uint64           `json:"quoteAmountInWithLpFee"` // Note: This field name was inferred
	UserQuoteAmountIn                uint64           `json:"userQuoteAmountIn"`
	Pool                             solana.PublicKey `json:"pool"`
	User                             solana.PublicKey `json:"user"`
	UserBaseTokenAccount             solana.PublicKey `json:"userBaseTokenAccount"`
	UserQuoteTokenAccount            solana.PublicKey `json:"userQuoteTokenAccount"`
	ProtocolFeeRecipient             solana.PublicKey `json:"protocolFeeRecipient"`
	ProtocolFeeRecipientTokenAccount solana.PublicKey `json:"protocolFeeRecipientTokenAccount"`
	CoinCreator                      solana.PublicKey `json:"coinCreator"`
	CoinCreatorFeeBasisPoints        uint64           `json:"coinCreatorFeeBasisPoints"`
	CoinCreatorFee                   uint64           `json:"coinCreatorFee"`
}

type PumpSwapSellEvent struct {
	Timestamp                        int64            `json:"timestamp"`
	BaseAmountIn                     uint64           `json:"baseAmountIn"`
	MinQuoteAmountOut                uint64           `json:"minQuoteAmountOut"`
	UserBaseTokenReserves            uint64           `json:"userBaseTokenReserves"`
	UserQuoteTokenReserves           uint64           `json:"userQuoteTokenReserves"`
	PoolBaseTokenReserves            uint64           `json:"poolBaseTokenReserves"`
	PoolQuoteTokenReserves           uint64           `json:"poolQuoteTokenReserves"`
	QuoteAmountOut                   uint64           `json:"quoteAmountOut"`
	LpFeeBasisPoints                 uint64           `json:"lpFeeBasisPoints"`
	LpFee                            uint64           `json:"lpFee"`
	ProtocolFeeBasisPoints           uint64           `json:"protocolFeeBasisPoints"`
	ProtocolFee                      uint64           `json:"protocolFee"`
	QuoteAmountOutWithoutLpFee       uint64           `json:"quoteAmountOutWithoutLpFee"`
	UserQuoteAmountOut               uint64           `json:"userQuoteAmountOut"`
	Pool                             solana.PublicKey `json:"pool"`
	User                             solana.PublicKey `json:"user"`
	UserBaseTokenAccount             solana.PublicKey `json:"userBaseTokenAccount"`
	UserQuoteTokenAccount            solana.PublicKey `json:"userQuoteTokenAccount"`
	ProtocolFeeRecipient             solana.PublicKey `json:"protocolFeeRecipient"`
	ProtocolFeeRecipientTokenAccount solana.PublicKey `json:"protocolFeeRecipientTokenAccount"`
	CoinCreator                      solana.PublicKey `json:"coinCreator"`
	CoinCreatorFeeBasisPoints        uint64           `json:"coinCreatorFeeBasisPoints"`
	CoinCreatorFee                   uint64           `json:"coinCreatorFee"`
}

var (
	PumpSwapSellEventDiscriminator   = [16]byte{228, 69, 165, 46, 81, 203, 154, 29, 62, 47, 55, 10, 165, 3, 220, 42}
	PumpSwapBuyEventDiscriminator    = [16]byte{228, 69, 165, 46, 81, 203, 154, 29, 103, 244, 82, 31, 44, 245, 119, 119}
	PumpSwapCreateEventDiscriminator = [16]byte{228, 69, 165, 46, 81, 203, 154, 29, 177, 49, 12, 210, 160, 118, 167, 116}
)

func (p *Parser) processPumpSwapSwaps(instructionIndex int) []SwapData {
	var swaps []SwapData
	for _, innerInstructionSet := range p.txMeta.InnerInstructions {
		if innerInstructionSet.Index == uint32(instructionIndex) {
			for _, innerInstruction := range innerInstructionSet.Instructions {
				if p.isPumpSwapBuyEventInstruction(innerInstruction) {
					eventData, err := p.parsePumpSwapBuyEventInstruction(innerInstruction)
					if err != nil {
						p.Log.Errorf("error processing PumpSwap trade event: %s", err)
						continue
					}
					if eventData != nil {
						if len(innerInstructionSet.Instructions) > 1 &&
							len(innerInstructionSet.Instructions[0].Accounts) > 1 &&
							len(innerInstructionSet.Instructions[1].Accounts) > 1 {
							eventData.UserBaseTokenAccount = p.allAccountKeys[innerInstructionSet.Instructions[1].Accounts[1]]
							eventData.UserQuoteTokenAccount = p.allAccountKeys[innerInstructionSet.Instructions[0].Accounts[1]]
							swaps = append(swaps, SwapData{Type: PUMP_SWAP, TxType: "BUY", InstructionIndex: instructionIndex, Data: eventData})
						}
					}
				} else if p.isPumpSwapSellEventInstruction(innerInstruction) {
					eventData, err := p.parsePumpSwapSellEventInstruction(innerInstruction)
					if err != nil {
						p.Log.Errorf("error processing PumpSwap sell event: %s", err)
						continue
					}
					if eventData != nil {
						if len(innerInstructionSet.Instructions) > 1 &&
							len(innerInstructionSet.Instructions[0].Accounts) > 1 &&
							len(innerInstructionSet.Instructions[1].Accounts) > 1 {
							eventData.UserBaseTokenAccount = p.allAccountKeys[innerInstructionSet.Instructions[0].Accounts[1]]
							eventData.UserQuoteTokenAccount = p.allAccountKeys[innerInstructionSet.Instructions[1].Accounts[1]]
							swaps = append(swaps, SwapData{Type: PUMP_SWAP, TxType: "SELL", InstructionIndex: instructionIndex, Data: eventData})
						}
					}
				}
			}
		}
	}
	return swaps
}

func (p *Parser) parsePumpSwapEventInstruction(instruction *proto.InnerInstruction) (*PumpSwapCreateEvent, error) {
	decodedBytes := instruction.Data
	decoder := ag_binary.NewBorshDecoder(decodedBytes[16:])
	return handlePumpSwapCreateEvent(decoder)
}
func handlePumpSwapCreateEvent(decoder *ag_binary.Decoder) (*PumpSwapCreateEvent, error) {
	var trade PumpSwapCreateEvent
	if err := decoder.Decode(&trade); err != nil {
		return nil, fmt.Errorf("error unmarshaling TradeEvent: %s", err)
	}

	return &trade, nil
}

func (p *Parser) parsePumpSwapBuyEventInstruction(instruction *proto.InnerInstruction) (*PumpSwapBuyEvent, error) {
	decodedBytes := instruction.Data
	decoder := ag_binary.NewBorshDecoder(decodedBytes[16:])
	return handlePumpSwapBuyEvent(decoder)
}
func handlePumpSwapBuyEvent(decoder *ag_binary.Decoder) (*PumpSwapBuyEvent, error) {
	var trade PumpSwapBuyEvent
	if err := decoder.Decode(&trade); err != nil {
		return nil, fmt.Errorf("error unmarshaling TradeEvent: %s", err)
	}

	return &trade, nil
}

func (p *Parser) parsePumpSwapSellEventInstruction(instruction *proto.InnerInstruction) (*PumpSwapSellEvent, error) {
	decodedBytes := instruction.Data
	decoder := ag_binary.NewBorshDecoder(decodedBytes[16:])
	return handlePumpSwapSellEvent(decoder)
}
func handlePumpSwapSellEvent(decoder *ag_binary.Decoder) (*PumpSwapSellEvent, error) {
	var trade PumpSwapSellEvent
	if err := decoder.Decode(&trade); err != nil {
		return nil, fmt.Errorf("error unmarshaling TradeEvent: %s", err)
	}

	return &trade, nil
}
