package txparser

import (
	"testing"
	"time"

	"model-processor/proto"

	"github.com/gagliardetto/solana-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMultiSignerTransaction 测试多签名交易的处理
func TestMultiSignerTransaction(t *testing.T) {
	// 创建测试用的多个签名者
	signer1 := solana.NewWallet().PublicKey()
	signer2 := solana.NewWallet().PublicKey()
	programID := solana.MustPublicKeyFromBase58("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4") // Jupiter

	// 创建测试交易
	tx := &proto.Transaction{
		Signatures: [][]byte{
			make([]byte, 64), // signer1 的签名
			make([]byte, 64), // signer2 的签名
		},
		Message: &proto.Message{
			AccountKeys: [][]byte{
				signer1.Bytes(),
				signer2.Bytes(),
				programID.Bytes(),
				// 其他账户...
			},
			Instructions: []*proto.CompiledInstruction{
				{
					ProgramIdIndex: 2,
					Accounts:       []byte{0, 3, 4}, // 第一个指令使用 signer1
				},
				{
					ProgramIdIndex: 2,
					Accounts:       []byte{1, 5, 6}, // 第二个指令使用 signer2
				},
			},
		},
	}

	txMeta := &proto.TransactionStatusMeta{
		InnerInstructions: []*proto.InnerInstructions{
			{
				Index:        0,
				Instructions: []*proto.InnerInstruction{
					// 模拟内部指令
				},
			},
			{
				Index:        1,
				Instructions: []*proto.InnerInstruction{
					// 模拟内部指令
				},
			},
		},
	}

	// 创建解析器
	parser := &Parser{
		txInfo:          tx,
		txMeta:          txMeta,
		allAccountKeys:  []solana.PublicKey{signer1, signer2, programID},
		splDecimalsMap:  make(map[string]uint32),
		splTokenInfoMap: make(map[string]TokenInfo),
	}

	// 创建测试用的 SwapData
	swapDatas := []SwapData{
		{
			Type:             RAYDIUM,
			InstructionIndex: 0,
			Data:             &TransferData{}, // 模拟数据
		},
		{
			Type:             RAYDIUM,
			InstructionIndex: 1,
			Data:             &TransferData{}, // 模拟数据
		},
	}

	// 处理交易数据
	swapInfos, err := parser.ProcessSwapData(swapDatas)
	require.NoError(t, err)
	require.Len(t, swapInfos, 2)

	// 验证每个 SwapInfo 使用了正确的 signer
	// 由于我们修复了代码，现在应该为每个指令提取正确的 signer
	assert.Equal(t, signer1.String(), swapInfos[0].User.String(), "第一个交易应该使用 signer1")
	assert.Equal(t, signer2.String(), swapInfos[1].User.String(), "第二个交易应该使用 signer2")

	// 验证指令索引正确设置
	assert.Equal(t, 0, swapInfos[0].InstructionIndex)
	assert.Equal(t, 1, swapInfos[1].InstructionIndex)

	// 验证 Signers 数组包含所有签名者
	assert.Len(t, swapInfos[0].Signers, 2, "应该包含所有签名者")
	assert.Len(t, swapInfos[1].Signers, 2, "应该包含所有签名者")
}

// TestPumpfunMultiSigner 测试 Pump.fun 协议的多签名处理
func TestPumpfunMultiSigner(t *testing.T) {
	// 创建测试用的用户地址
	user1 := solana.NewWallet().PublicKey()
	user2 := solana.NewWallet().PublicKey()
	mintAddr := solana.NewWallet().PublicKey()

	// 创建 Pump.fun 事件数据
	event1 := &PumpfunTradeEvent{
		User:        user1,
		Mint:        mintAddr,
		IsBuy:       true,
		SolAmount:   1000000000, // 1 SOL
		TokenAmount: 1000000,    // 1M tokens
		Timestamp:   time.Now().Unix(),
	}

	event2 := &PumpfunTradeEvent{
		User:        user2,
		Mint:        mintAddr,
		IsBuy:       false,
		SolAmount:   500000000, // 0.5 SOL
		TokenAmount: 500000,    // 0.5M tokens
		Timestamp:   time.Now().Unix(),
	}

	swapDatas := []SwapData{
		{
			Type:             PUMP_FUN,
			InstructionIndex: 0,
			Data:             event1,
			TxType:           "BUY",
		},
		{
			Type:             PUMP_FUN,
			InstructionIndex: 1,
			Data:             event2,
			TxType:           "SELL",
		},
	}

	// 创建解析器
	parser := &Parser{
		txInfo: &proto.Transaction{
			Signatures: [][]byte{
				make([]byte, 64),
				make([]byte, 64),
			},
		},
		allAccountKeys:  []solana.PublicKey{user1, user2},
		splDecimalsMap:  map[string]uint32{mintAddr.String(): 6},
		splTokenInfoMap: make(map[string]TokenInfo),
	}

	// 处理交易数据
	swapInfos, err := parser.ProcessSwapData(swapDatas)
	require.NoError(t, err)
	require.Len(t, swapInfos, 2)

	// 验证用户地址正确
	assert.Equal(t, user1.String(), swapInfos[0].User.String(), "第一个交易应该使用 user1")
	assert.Equal(t, user2.String(), swapInfos[1].User.String(), "第二个交易应该使用 user2")

	// 验证交易类型
	assert.Equal(t, "BUY", swapInfos[0].TxType)
	assert.Equal(t, "SELL", swapInfos[1].TxType)

	// 验证代币信息
	assert.Equal(t, NATIVE_SOL_MINT_PROGRAM_ID.String(), swapInfos[0].TokenInMint.String())
	assert.Equal(t, mintAddr.String(), swapInfos[0].TokenOutMint.String())
	assert.Equal(t, mintAddr.String(), swapInfos[1].TokenInMint.String())
	assert.Equal(t, NATIVE_SOL_MINT_PROGRAM_ID.String(), swapInfos[1].TokenOutMint.String())
}

// TestExtractSignerForInstruction 测试 signer 提取逻辑
func TestExtractSignerForInstruction(t *testing.T) {
	signer1 := solana.NewWallet().PublicKey()
	signer2 := solana.NewWallet().PublicKey()
	signer3 := solana.NewWallet().PublicKey()

	parser := &Parser{
		txInfo: &proto.Transaction{
			Signatures: [][]byte{
				make([]byte, 64),
				make([]byte, 64),
				make([]byte, 64),
			},
			Message: &proto.Message{
				Instructions: []*proto.CompiledInstruction{
					{
						ProgramIdIndex: 3,
						Accounts:       []byte{0, 4, 5}, // 使用 signer1
					},
					{
						ProgramIdIndex: 3,
						Accounts:       []byte{1, 6, 7}, // 使用 signer2
					},
					{
						ProgramIdIndex: 3,
						Accounts:       []byte{2, 8, 9}, // 使用 signer3
					},
				},
			},
		},
		allAccountKeys: []solana.PublicKey{signer1, signer2, signer3},
	}

	// 测试为不同指令提取正确的 signer
	assert.Equal(t, signer1.String(), parser.extractSignerForInstruction(0).String())
	assert.Equal(t, signer2.String(), parser.extractSignerForInstruction(1).String())
	assert.Equal(t, signer3.String(), parser.extractSignerForInstruction(2).String())

	// 测试边界情况
	assert.Equal(t, signer1.String(), parser.extractSignerForInstruction(-1).String()) // 负数索引应该回退到第一个
	assert.Equal(t, signer1.String(), parser.extractSignerForInstruction(10).String()) // 超出范围应该回退到第一个
}

// TestSingleSignerBackwardCompatibility 测试单签名交易的向后兼容性
func TestSingleSignerBackwardCompatibility(t *testing.T) {
	signer := solana.NewWallet().PublicKey()

	parser := &Parser{
		txInfo: &proto.Transaction{
			Signatures: [][]byte{
				make([]byte, 64),
			},
		},
		allAccountKeys:  []solana.PublicKey{signer},
		splDecimalsMap:  make(map[string]uint32),
		splTokenInfoMap: make(map[string]TokenInfo),
	}

	swapDatas := []SwapData{
		{
			Type:             RAYDIUM,
			InstructionIndex: 0,
			Data:             &TransferData{},
		},
	}

	swapInfos, err := parser.ProcessSwapData(swapDatas)
	require.NoError(t, err)
	require.Len(t, swapInfos, 1)

	// 验证单签名交易仍然正常工作
	assert.Equal(t, signer.String(), swapInfos[0].User.String())
	assert.Len(t, swapInfos[0].Signers, 1)
	assert.Equal(t, signer.String(), swapInfos[0].Signers[0].String())
}

// BenchmarkProcessSwapDataMultiSigner 性能测试
func BenchmarkProcessSwapDataMultiSigner(b *testing.B) {
	// 创建测试数据
	signers := make([]solana.PublicKey, 10)
	signatures := make([][]byte, 10)
	for i := 0; i < 10; i++ {
		signers[i] = solana.NewWallet().PublicKey()
		signatures[i] = make([]byte, 64)
	}

	parser := &Parser{
		txInfo: &proto.Transaction{
			Signatures: signatures,
		},
		allAccountKeys:  signers,
		splDecimalsMap:  make(map[string]uint32),
		splTokenInfoMap: make(map[string]TokenInfo),
	}

	swapDatas := make([]SwapData, 10)
	for i := 0; i < 10; i++ {
		swapDatas[i] = SwapData{
			Type:             RAYDIUM,
			InstructionIndex: i,
			Data:             &TransferData{},
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := parser.ProcessSwapData(swapDatas)
		if err != nil {
			b.Fatal(err)
		}
	}
}
